# 🚀 自动化模型文档生成系统

## Enhanced Report Generation System

一个基于Python的自动化系统，用于高效、规范地生成机器学习项目（特别是逻辑回归）的完整技术文档套件，包含Word文档和PowerPoint演示文稿。

## 📋 项目特性

### 🎯 核心功能
- **自动化文档生成**: 一键生成专业的Word技术报告和PPT演示文稿
- **智能文件识别**: 自动扫描和分类各种类型的报告文件
- **模块化设计**: 支持分步执行和单独章节生成
- **配置驱动**: 通过YAML配置文件灵活控制报告结构和样式
- **跨平台兼容**: 支持Windows、macOS、Linux等主流操作系统

### 📊 支持的文件类型
- **Excel文件** (.xlsx, .xls): 数据表、统计结果、变量列表等
- **图片文件** (.png, .jpg, .jpeg, .gif, .bmp): ROC曲线、变量重要性图、数据分布图等
- **文本文件** (.txt, .csv, .md): 分析说明、日志信息等
- **代码文件** (.py, .sql, .r, .sas): 建模代码、部署脚本等

### 🎨 文档特性
- **专业排版**: 统一的字体、样式和格式
- **智能转换**: Excel表格自动转换为高质量图片
- **中文支持**: 完善的中文字体处理和显示
- **结构化内容**: 按照机器学习工作流组织章节
- **交互式控制**: 支持用户干预和自定义选择

## 🛠️ 安装和配置

### 环境要求
- Python 3.7+
- 推荐使用虚拟环境

### 安装步骤

1. **克隆或下载项目**
```bash
git clone <repository-url>
cd enhanced-report-generator
```

2. **安装依赖包**
```bash
pip install -r requirements.txt
```

3. **配置系统**
   - 编辑 `config/enhanced_config.yaml` 文件
   - 设置报告源目录路径
   - 调整文档样式和格式选项

### 配置文件说明

主要配置项：

```yaml
# 路径配置
paths:
  report_dir: "rpt/Report_01"    # 报告源文件目录
  output_dir: "output"           # 输出目录
  
# 文档元数据
document:
  title: "机器学习模型技术报告"
  author: "数据科学团队"
  company: "公司名称"
  
# 章节结构
sections:
  - name: "数据准备与探索性分析"
    file_patterns: ["01_.*\\.xlsx", ".*数据.*\\.xlsx"]
    
# 样式配置
styling:
  fonts:
    priority: ["PingFang SC", "Microsoft YaHei", "SimHei"]
  excel_as_image: true
```

## 🚀 使用方法

### 快速开始

1. **准备报告文件**
   - 将所有报告文件放入指定的源目录
   - 建议使用有意义的文件命名（如：01_数据概览.xlsx）

2. **运行报告生成器**
```bash
python run_report_generator.py
```

3. **选择操作**
   - 生成完整报告 (Word + PPT)
   - 仅生成Word报告
   - 生成指定章节

### 命令行使用

```bash
# 生成完整报告
python src/enhanced_report_generator.py

# 生成指定章节
python src/enhanced_report_generator.py --section "数据准备与探索性分析"

# 列出可用章节
python src/enhanced_report_generator.py --list-sections

# 交互式模式
python src/enhanced_report_generator.py --interactive

# 使用自定义配置
python src/enhanced_report_generator.py --config custom_config.yaml
```

## 📁 项目结构

```
enhanced-report-generator/
├── config/
│   └── enhanced_config.yaml      # 主配置文件
├── src/
│   ├── core/
│   │   └── base_generator.py     # 基础生成器
│   ├── processors/
│   │   └── file_processor.py     # 文件处理器
│   ├── generators/
│   │   ├── word_generator.py     # Word生成器
│   │   └── ppt_generator.py      # PPT生成器
│   └── enhanced_report_generator.py  # 主生成器
├── rpt/
│   └── Report_01/                # 示例报告文件
├── output/                       # 输出目录
├── temp/                         # 临时文件目录
├── run_report_generator.py       # 启动脚本
├── requirements.txt              # 依赖包列表
└── README.md                     # 项目说明
```

## 📖 详细功能说明

### 文件匹配规则

系统使用正则表达式匹配文件名，将文件自动分类到相应章节：

- `01_.*\.xlsx` - 匹配以"01_"开头的Excel文件
- `.*变量.*\.png` - 匹配文件名包含"变量"的PNG图片
- `.*模型.*\.py` - 匹配文件名包含"模型"的Python代码

### 章节结构

默认报告结构遵循机器学习建模流程：

1. **项目概述** - 项目背景和目标
2. **数据准备与探索性分析** - 数据清洗和探索
3. **特征工程与变量分析** - 变量筛选和分箱
4. **模型构建与参数** - 模型训练和参数
5. **模型评估与验证** - 性能评估和验证
6. **变量相关性分析** - 相关性检验
7. **评分卡与部署** - 部署材料和代码

### Excel处理

- **自动转图片**: 将Excel表格转换为高质量PNG图片
- **多工作表支持**: 处理包含多个工作表的Excel文件
- **数据预览**: 限制显示行数，避免过长表格
- **格式保持**: 保持原有的数据格式和精度

### 图片处理

- **自动调整**: 根据配置自动调整图片尺寸
- **格式转换**: 统一转换为适合文档的格式
- **标题生成**: 从文件名自动生成图片标题
- **质量优化**: 平衡文件大小和显示质量

## 🔧 高级配置

### 自定义章节

在配置文件中添加新章节：

```yaml
sections:
  - id: 8
    name: "自定义分析"
    description: "特殊的分析内容"
    file_patterns: 
      - "custom_.*\\.xlsx"
      - "special_.*\\.png"
    content_type: "mixed"
```

### 样式定制

调整文档样式：

```yaml
styling:
  font_sizes:
    normal: 10.5
    heading1: 16
    heading2: 14
  dimensions:
    table_width: 15  # cm
    image_width: 12  # cm
  processing:
    excel_as_image: true
    image_quality: "high"
```

### PPT模板

自定义PPT样式：

```yaml
ppt:
  generate: true
  dimensions:
    width: 25.4   # cm
    height: 14.29 # cm
  styles:
    title_font_size: 24
    content_font_size: 18
```

## 🐛 故障排除

### 常见问题

1. **字体显示问题**
   - 确保系统安装了配置中指定的中文字体
   - 可以在配置文件中调整字体优先级

2. **文件路径错误**
   - 检查配置文件中的路径是否正确
   - 使用绝对路径或相对于工作目录的路径

3. **Excel文件读取失败**
   - 确保Excel文件没有被其他程序占用
   - 检查文件是否损坏或格式不支持

4. **内存不足**
   - 处理大量图片时可能消耗较多内存
   - 可以调整图片质量设置或分批处理

### 日志查看

系统会生成详细的日志文件：

```bash
# 查看日志文件
cat output/report_generation.log

# 实时查看日志
tail -f output/report_generation.log
```

## 🤝 贡献指南

欢迎提交问题报告和功能建议！

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

如有问题或建议，请：

1. 查看本文档的故障排除部分
2. 提交 GitHub Issue
3. 联系项目维护者

---

**注意**: 本系统专为机器学习建模项目设计，特别适用于逻辑回归等传统机器学习算法的文档化需求。
