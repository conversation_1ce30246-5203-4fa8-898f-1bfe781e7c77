# 增强版报告生成配置文件
# Enhanced Report Generation Configuration

# 文档元数据
document:
  title: "机器学习模型技术报告"
  subtitle: "逻辑回归建模完整技术文档"
  author: "数据科学团队"
  company: "公司名称"
  version: "1.0"
  project_name: "逻辑回归建模项目"
  
# 路径配置
paths:
  # 报告源文件目录（相对于工作目录）
  report_dir: "rpt/Report_01"
  # 输出目录
  output_dir: "output"
  # 模板目录
  template_dir: "templates"
  # 临时文件目录
  temp_dir: "temp"
  # 最大搜索深度
  max_search_depth: 3
  # 是否递归搜索子目录
  recursive_search: true

# 报告章节结构配置
sections:
  - id: 1
    name: "项目概述"
    description: "项目背景、目标和主要发现"
    file_patterns: 
      - ".*概述.*\\.(xlsx|txt|md)"
      - ".*summary.*\\.(xlsx|txt|md)"
    content_type: "mixed"
    
  - id: 2
    name: "数据准备与探索性分析"
    description: "数据源、清洗、探索性分析"
    file_patterns:
      - "01_.*\\.xlsx"
      - ".*数据.*\\.xlsx"
      - ".*data.*\\.(xlsx|png|jpg)"
    content_type: "data_analysis"
    
  - id: 3
    name: "特征工程与变量分析"
    description: "变量筛选、IV值、KS统计、分箱分析"
    file_patterns:
      - "02_.*\\.(xlsx|png|jpg)"
      - ".*变量.*\\.(xlsx|png|jpg)"
      - ".*IV.*\\.(xlsx|png|jpg)"
      - ".*KS.*\\.(xlsx|png|jpg)"
      - ".*分箱.*\\.(xlsx|png|jpg)"
    content_type: "feature_engineering"
    
  - id: 4
    name: "模型构建与参数"
    description: "模型训练、参数估计、系数分析"
    file_patterns:
      - "03_.*\\.xlsx"
      - ".*模型.*\\.xlsx"
      - ".*系数.*\\.xlsx"
      - ".*参数.*\\.xlsx"
    content_type: "model_building"
    
  - id: 5
    name: "模型评估与验证"
    description: "模型性能评估、ROC曲线、混淆矩阵"
    file_patterns:
      - "04_.*\\.(xlsx|png|jpg)"
      - ".*效果.*\\.(xlsx|png|jpg)"
      - ".*评估.*\\.(xlsx|png|jpg)"
      - ".*ROC.*\\.(png|jpg)"
      - ".*AUC.*\\.(png|jpg)"
    content_type: "model_evaluation"
    
  - id: 6
    name: "变量相关性分析"
    description: "变量间相关性、多重共线性检验"
    file_patterns:
      - "05_.*\\.xlsx"
      - ".*相关.*\\.(xlsx|png|jpg)"
      - ".*correlation.*\\.(xlsx|png|jpg)"
    content_type: "correlation_analysis"
    
  - id: 7
    name: "评分卡与部署"
    description: "评分卡构建、部署代码、业务应用"
    file_patterns:
      - "06_.*\\.xlsx"
      - ".*评分.*\\.(xlsx|py|sql)"
      - ".*部署.*\\.(py|sql|txt)"
      - ".*Code.*\\.(py|sql)"
    content_type: "deployment"

# 附录配置
appendix:
  - name: "详细数据字典"
    file_patterns:
      - ".*字典.*\\.xlsx"
      - ".*dictionary.*\\.xlsx"
  - name: "完整代码清单"
    file_patterns:
      - ".*\\.py"
      - ".*\\.sql"
  - name: "补充图表"
    file_patterns:
      - ".*补充.*\\.(png|jpg)"
      - ".*additional.*\\.(png|jpg)"

# 样式和格式配置
styling:
  # 字体配置
  fonts:
    priority: ["PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "SimHei", "SimSun"]
    fallback: "Arial Unicode MS"
    code_font: ["Consolas", "Monaco", "Courier New"]
  
  # 字体大小
  font_sizes:
    normal: 10.5
    heading1: 16
    heading2: 14
    heading3: 12
    caption: 9
    code: 9
  
  # 页面设置
  page:
    margins:
      top: 2.5
      bottom: 2.5
      left: 2.0
      right: 2.0
    orientation: "portrait"  # portrait 或 landscape
  
  # 表格和图片尺寸
  dimensions:
    table_width: 15  # cm
    image_width: 12  # cm
    max_image_height: 8  # cm
    ppt_image_width: 20  # cm
  
  # 处理选项
  processing:
    excel_as_image: true
    image_quality: "high"  # low, medium, high
    compress_images: false
    auto_resize_images: true
  
  # Word文档样式
  word_styles:
    line_spacing: 1.15
    paragraph_spacing_before: 6
    paragraph_spacing_after: 6
    table_style: "Table Grid"
    
# PPT配置
ppt:
  generate: true
  template: "default"
  dimensions:
    width: 25.4   # cm (10 inches)
    height: 14.29 # cm (5.63 inches)
  layouts:
    title_slide: 0
    content_slide: 1
    two_content: 3
    blank: 6
  styles:
    title_font_size: 24
    content_font_size: 18
    bullet_font_size: 16

# 高级功能配置
advanced:
  # 交互式控制
  interactive_mode: true
  confirm_before_processing: false
  
  # 模块化执行
  modular_execution: true
  save_intermediate_results: true
  
  # 错误处理
  continue_on_error: true
  max_errors_per_section: 3
  
  # 性能优化
  parallel_processing: false
  max_workers: 4
  cache_processed_files: true
  
  # 日志配置
  logging:
    level: "INFO"  # DEBUG, INFO, WARNING, ERROR
    file: "report_generation.log"
    console_output: true

# 文件类型处理规则
file_handlers:
  excel:
    extensions: [".xlsx", ".xls"]
    max_rows_preview: 20
    sheet_selection: "auto"  # auto, first, all, specific
    specific_sheets: []
    
  images:
    extensions: [".png", ".jpg", ".jpeg", ".gif", ".bmp", ".tiff"]
    auto_caption: true
    caption_from_filename: true
    
  text:
    extensions: [".txt", ".md", ".csv"]
    encoding: "utf-8"
    max_lines_preview: 50
    
  code:
    extensions: [".py", ".sql", ".r", ".sas"]
    syntax_highlighting: true
    include_comments: true
    max_lines_preview: 100

# 质量控制
quality_control:
  # 内容验证
  validate_file_existence: true
  check_image_quality: true
  verify_excel_readability: true
  
  # 输出验证
  validate_word_structure: true
  check_ppt_completeness: true
  verify_cross_references: true
  
  # 性能监控
  track_processing_time: true
  monitor_memory_usage: true
  log_file_sizes: true
