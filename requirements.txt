# 自动化模型文档生成系统依赖包
# Enhanced Report Generation System Dependencies

# 核心文档处理库
python-docx>=0.8.11          # Word文档处理
python-pptx>=0.6.21          # PowerPoint演示文稿处理

# 数据处理库
pandas>=1.3.0                # 数据分析和Excel处理
numpy>=1.21.0                # 数值计算
openpyxl>=3.0.9              # Excel文件读写

# 图像处理库
Pillow>=8.3.0                # 图像处理和格式转换
matplotlib>=3.4.0            # 图表生成和数据可视化

# 配置和文本处理
PyYAML>=5.4.0                # YAML配置文件解析
pathlib2>=2.3.6; python_version<"3.4"  # 路径处理（Python 3.4以下版本）

# 可选的增强功能
# dataframe-image>=0.1.1     # DataFrame直接转图片（可选）
# plotly>=5.0.0              # 交互式图表（可选）
# seaborn>=0.11.0            # 统计图表美化（可选）

# 开发和测试工具（可选）
# pytest>=6.0.0             # 单元测试
# black>=21.0.0              # 代码格式化
# flake8>=3.9.0              # 代码检查
