#!/usr/bin/env python3
"""
报告生成器启动脚本
Report Generator Launcher Script

提供简单的命令行接口来运行报告生成器
"""

import os
import sys
from pathlib import Path

# 添加src目录到Python路径
current_dir = Path(__file__).parent
src_dir = current_dir / "src"
sys.path.insert(0, str(src_dir))

from enhanced_report_generator import EnhancedReportGenerator


def main():
    """主函数"""
    print("=" * 60)
    print("🚀 自动化模型文档生成系统")
    print("   Enhanced Report Generation System")
    print("=" * 60)
    
    try:
        # 检查配置文件
        config_path = current_dir / "config" / "enhanced_config.yaml"
        if not config_path.exists():
            print(f"❌ 配置文件不存在: {config_path}")
            print("请确保配置文件存在并正确配置")
            return False
        
        print(f"📋 使用配置文件: {config_path}")
        
        # 创建报告生成器
        print("🔧 初始化报告生成器...")
        generator = EnhancedReportGenerator(
            config_path=str(config_path),
            work_dir=str(current_dir)
        )
        
        # 显示可用章节
        print("\n📚 扫描可用章节...")
        sections = generator.list_available_sections()
        
        if not sections:
            print("❌ 未找到任何可处理的文件")
            print("请检查报告源目录和文件匹配规则")
            return False
        
        print(f"✅ 找到 {len(sections)} 个章节:")
        for i, section in enumerate(sections, 1):
            print(f"   {i}. {section}")
        
        # 询问用户操作
        print("\n🎯 请选择操作:")
        print("1. 生成完整报告 (Word + PPT)")
        print("2. 仅生成Word报告")
        print("3. 生成指定章节")
        print("4. 退出")
        
        while True:
            choice = input("\n请输入选择 (1-4): ").strip()
            
            if choice == '1':
                print("\n🚀 开始生成完整报告...")
                success, message = generator.generate_report()
                break
                
            elif choice == '2':
                print("\n🚀 开始生成Word报告...")
                # 临时禁用PPT生成
                original_ppt_setting = generator.config['ppt']['generate']
                generator.config['ppt']['generate'] = False
                generator.ppt_generator = None
                
                success, message = generator.generate_report()
                
                # 恢复设置
                generator.config['ppt']['generate'] = original_ppt_setting
                break
                
            elif choice == '3':
                print("\n📋 可用章节:")
                for i, section in enumerate(sections, 1):
                    print(f"   {i}. {section}")
                
                section_input = input("\n请输入章节编号或名称: ").strip()
                
                if section_input.isdigit():
                    section_idx = int(section_input) - 1
                    if 0 <= section_idx < len(sections):
                        section_name = sections[section_idx]
                        print(f"\n🚀 开始生成章节: {section_name}")
                        success, message = generator.generate_section_only(section_name)
                        break
                    else:
                        print("❌ 无效的章节编号")
                        continue
                else:
                    if section_input in sections:
                        print(f"\n🚀 开始生成章节: {section_input}")
                        success, message = generator.generate_section_only(section_input)
                        break
                    else:
                        print("❌ 未找到指定章节")
                        continue
                        
            elif choice == '4':
                print("👋 退出程序")
                return True
                
            else:
                print("❌ 无效选择，请重新输入")
                continue
        
        # 显示结果
        print("\n" + "=" * 60)
        if success:
            print("✅ " + message)
            
            # 显示输出文件
            output_dir = Path(generator.config['paths']['output_dir'])
            output_files = list(output_dir.glob("*.docx")) + list(output_dir.glob("*.pptx"))
            
            if output_files:
                print("\n📁 生成的文件:")
                for file_path in output_files:
                    file_size = file_path.stat().st_size / 1024  # KB
                    print(f"   📄 {file_path.name} ({file_size:.1f} KB)")
                    print(f"      路径: {file_path}")
            
            # 显示处理统计
            stats = generator.get_processing_stats()
            print(f"\n📊 处理统计:")
            print(f"   ⏱️  处理时间: {stats.get('end_time', 'N/A') - stats.get('start_time', 'N/A') if stats.get('end_time') and stats.get('start_time') else 'N/A'}")
            print(f"   📁 处理文件数: {stats.get('files_processed', 0)}")
            print(f"   📚 完成章节数: {stats.get('sections_completed', 0)}")
            print(f"   ⚠️  错误数量: {stats.get('errors_count', 0)}")
            
        else:
            print("❌ " + message)
            print("\n💡 建议检查:")
            print("   1. 配置文件是否正确")
            print("   2. 报告源目录是否存在")
            print("   3. 文件权限是否足够")
            print("   4. 查看日志文件获取详细错误信息")
        
        print("=" * 60)
        return success
        
    except KeyboardInterrupt:
        print("\n\n⏹️  用户中断操作")
        return False
        
    except Exception as e:
        print(f"\n❌ 程序执行失败: {str(e)}")
        print("\n💡 请检查:")
        print("   1. Python环境和依赖包是否正确安装")
        print("   2. 配置文件格式是否正确")
        print("   3. 文件路径是否存在")
        return False


def check_dependencies():
    """检查依赖包"""
    required_packages = [
        ('docx', 'python-docx'),
        ('pptx', 'python-pptx'),
        ('pandas', 'pandas'),
        ('PIL', 'Pillow'),
        ('matplotlib', 'matplotlib'),
        ('yaml', 'PyYAML')
    ]
    
    missing_packages = []
    
    for package, pip_name in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(pip_name)
    
    if missing_packages:
        print("❌ 缺少必要的Python包:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n💡 请运行以下命令安装:")
        print(f"   pip install {' '.join(missing_packages)}")
        return False
    
    return True


if __name__ == '__main__':
    print("🔍 检查依赖包...")
    if not check_dependencies():
        sys.exit(1)
    
    print("✅ 依赖包检查通过")
    
    success = main()
    sys.exit(0 if success else 1)
