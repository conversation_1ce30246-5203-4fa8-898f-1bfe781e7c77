"""
基础报告生成器模块
Base Report Generator Module

提供报告生成的核心基础功能和抽象接口
"""

import os
import sys
import yaml
import logging
import traceback
from abc import ABC, abstractmethod
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import pandas as pd
from PIL import Image


class BaseReportGenerator(ABC):
    """报告生成器基类"""

    def __init__(self, config_path: str = None, work_dir: str = None):
        """
        初始化报告生成器

        Args:
            config_path: 配置文件路径
            work_dir: 工作目录
        """
        self.work_dir = Path(work_dir) if work_dir else Path.cwd()
        self.config_path = config_path or self.work_dir / "config" / "enhanced_config.yaml"

        # 初始化基础属性
        self.config = None
        self.logger = None
        self.temp_dir = None
        self.output_dir = None

        # 处理状态
        self.processing_stats = {
            'start_time': None,
            'end_time': None,
            'files_processed': 0,
            'errors_count': 0,
            'sections_completed': 0
        }

        # 初始化系统
        self._initialize()

    def _initialize(self):
        """初始化系统组件"""
        try:
            # 加载配置
            self.config = self._load_config()

            # 设置日志
            self._setup_logging()

            # 创建必要目录
            self._create_directories()

            # 验证环境
            self._validate_environment()

            self.logger.info("报告生成器初始化完成")

        except Exception as e:
            print(f"初始化失败: {str(e)}")
            raise

    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        config_path = Path(self.config_path)
        if not config_path.exists():
            raise FileNotFoundError(f"配置文件不存在: {config_path}")

        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)

        # 处理相对路径
        for key in ['report_dir', 'output_dir', 'template_dir', 'temp_dir']:
            if key in config['paths']:
                path = Path(config['paths'][key])
                if not path.is_absolute():
                    config['paths'][key] = str(self.work_dir / path)

        return config

    def _setup_logging(self):
        """设置日志系统"""
        log_config = self.config.get('advanced', {}).get('logging', {})

        # 创建logger
        self.logger = logging.getLogger('ReportGenerator')
        self.logger.setLevel(getattr(logging, log_config.get('level', 'INFO')))

        # 清除现有handlers
        self.logger.handlers.clear()

        # 文件handler
        if log_config.get('file'):
            log_file = Path(self.config['paths']['output_dir']) / log_config['file']
            # 确保日志文件目录存在
            log_file.parent.mkdir(parents=True, exist_ok=True)
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            file_handler.setFormatter(file_formatter)
            self.logger.addHandler(file_handler)

        # 控制台handler
        if log_config.get('console_output', True):
            console_handler = logging.StreamHandler()
            console_formatter = logging.Formatter(
                '%(levelname)s - %(message)s'
            )
            console_handler.setFormatter(console_formatter)
            self.logger.addHandler(console_handler)

    def _create_directories(self):
        """创建必要的目录"""
        dirs_to_create = [
            self.config['paths']['output_dir'],
            self.config['paths']['temp_dir']
        ]

        for dir_path in dirs_to_create:
            Path(dir_path).mkdir(parents=True, exist_ok=True)

        self.temp_dir = Path(self.config['paths']['temp_dir'])
        self.output_dir = Path(self.config['paths']['output_dir'])

    def _validate_environment(self):
        """验证运行环境"""
        # 检查必要的目录
        report_dir = Path(self.config['paths']['report_dir'])
        if not report_dir.exists():
            raise FileNotFoundError(f"报告源目录不存在: {report_dir}")

        # 检查Python包依赖
        required_packages = [
            'docx', 'pptx', 'pandas', 'PIL', 'matplotlib', 'yaml'
        ]

        missing_packages = []
        for package in required_packages:
            try:
                __import__(package)
            except ImportError:
                missing_packages.append(package)

        if missing_packages:
            raise ImportError(f"缺少必要的Python包: {missing_packages}")

    def get_font_config(self) -> Dict[str, Any]:
        """获取字体配置"""
        import matplotlib.font_manager as fm
        available_fonts = {f.name for f in fm.fontManager.ttflist}

        font_config = self.config['styling']['fonts']

        # 选择可用字体
        selected_font = None
        for font in font_config['priority']:
            if font in available_fonts:
                selected_font = font
                break

        if not selected_font:
            selected_font = font_config['fallback']
            self.logger.warning(f"使用备用字体: {selected_font}")
        else:
            self.logger.info(f"使用字体: {selected_font}")

        return {
            'main_font': selected_font,
            'code_font': font_config['code_font'][0],
            'available_fonts': available_fonts
        }

    def scan_files(self) -> Dict[str, List[str]]:
        """扫描并分类文件"""
        report_dir = Path(self.config['paths']['report_dir'])
        file_map = {}

        self.logger.info(f"开始扫描文件目录: {report_dir}")

        # 获取所有文件
        if self.config['paths'].get('recursive_search', True):
            all_files = list(report_dir.rglob('*'))
        else:
            all_files = list(report_dir.iterdir())

        # 过滤文件
        files = [f for f in all_files if f.is_file() and not f.name.startswith('.')]

        self.logger.info(f"找到 {len(files)} 个文件")

        # 按章节分类文件
        for section in self.config['sections']:
            section_files = []
            for pattern in section['file_patterns']:
                import re
                for file_path in files:
                    if re.search(pattern, file_path.name, re.IGNORECASE):
                        if str(file_path) not in [str(f) for f in section_files]:
                            section_files.append(file_path)

            file_map[section['name']] = section_files
            self.logger.info(f"章节 '{section['name']}' 匹配到 {len(section_files)} 个文件")

        return file_map

    def validate_file(self, file_path: Path) -> Tuple[bool, str]:
        """验证文件是否可以处理"""
        try:
            if not file_path.exists():
                return False, "文件不存在"

            if file_path.stat().st_size == 0:
                return False, "文件为空"

            # 根据文件类型进行特定验证
            suffix = file_path.suffix.lower()

            if suffix in ['.xlsx', '.xls']:
                try:
                    pd.read_excel(file_path, nrows=1)
                except Exception as e:
                    return False, f"Excel文件无法读取: {str(e)}"

            elif suffix in ['.png', '.jpg', '.jpeg', '.gif', '.bmp']:
                try:
                    with Image.open(file_path) as img:
                        img.verify()
                except Exception as e:
                    return False, f"图片文件损坏: {str(e)}"

            elif suffix in ['.txt', '.csv', '.md']:
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        f.read(100)  # 读取前100个字符测试
                except Exception as e:
                    return False, f"文本文件无法读取: {str(e)}"

            return True, "文件验证通过"

        except Exception as e:
            return False, f"验证过程出错: {str(e)}"

    def start_processing(self):
        """开始处理"""
        self.processing_stats['start_time'] = datetime.now()
        self.logger.info("开始报告生成处理")

    def end_processing(self):
        """结束处理"""
        self.processing_stats['end_time'] = datetime.now()
        duration = self.processing_stats['end_time'] - self.processing_stats['start_time']

        self.logger.info("报告生成处理完成")
        self.logger.info(f"处理时间: {duration}")
        self.logger.info(f"处理文件数: {self.processing_stats['files_processed']}")
        self.logger.info(f"错误数量: {self.processing_stats['errors_count']}")
        self.logger.info(f"完成章节数: {self.processing_stats['sections_completed']}")

    def handle_error(self, error: Exception, context: str = ""):
        """统一错误处理"""
        self.processing_stats['errors_count'] += 1
        error_msg = f"错误 [{context}]: {str(error)}"

        if self.config.get('advanced', {}).get('logging', {}).get('level') == 'DEBUG':
            error_msg += f"\n{traceback.format_exc()}"

        self.logger.error(error_msg)

        # 检查是否应该继续处理
        continue_on_error = self.config.get('advanced', {}).get('continue_on_error', True)
        max_errors = self.config.get('advanced', {}).get('max_errors_per_section', 3)

        if not continue_on_error or self.processing_stats['errors_count'] >= max_errors:
            raise error

    @abstractmethod
    def generate_report(self) -> Tuple[bool, str]:
        """生成报告的抽象方法"""
        pass

    def cleanup(self):
        """清理临时文件"""
        if self.temp_dir and self.temp_dir.exists():
            import shutil
            try:
                shutil.rmtree(self.temp_dir)
                self.logger.info("临时文件清理完成")
            except Exception as e:
                self.logger.warning(f"清理临时文件失败: {str(e)}")
