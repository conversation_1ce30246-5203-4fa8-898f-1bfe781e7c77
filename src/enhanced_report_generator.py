"""
增强版报告生成器
Enhanced Report Generator

整合所有模块，提供完整的报告生成功能
"""

import os
import sys
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import logging

# 添加src目录到Python路径
current_dir = Path(__file__).parent
src_dir = current_dir
sys.path.insert(0, str(src_dir))

from core.base_generator import BaseReportGenerator
from processors.file_processor import FileProcessor
from generators.word_generator import WordGenerator
from generators.ppt_generator import PPTGenerator


class EnhancedReportGenerator(BaseReportGenerator):
    """增强版报告生成器"""
    
    def __init__(self, config_path: str = None, work_dir: str = None):
        """
        初始化增强版报告生成器
        
        Args:
            config_path: 配置文件路径
            work_dir: 工作目录
        """
        super().__init__(config_path, work_dir)
        
        # 初始化处理器和生成器
        self.file_processor = FileProcessor(self.config, self.logger)
        self.word_generator = WordGenerator(self.config, self.logger)
        self.ppt_generator = PPTGenerator(self.config, self.logger) if self.config['ppt']['generate'] else None
        
        # 处理数据存储
        self.processed_data = {
            'sections': {},
            'appendix': {},
            'file_map': {},
            'processing_summary': {}
        }
    
    def generate_report(self) -> Tuple[bool, str]:
        """
        生成完整报告
        
        Returns:
            (成功标志, 结果消息)
        """
        try:
            self.start_processing()
            
            # 步骤1: 扫描和分类文件
            self.logger.info("=== 步骤1: 扫描和分类文件 ===")
            if not self._scan_and_classify_files():
                return False, "文件扫描失败"
            
            # 步骤2: 处理文件内容
            self.logger.info("=== 步骤2: 处理文件内容 ===")
            if not self._process_files():
                return False, "文件处理失败"
            
            # 步骤3: 生成Word文档
            self.logger.info("=== 步骤3: 生成Word文档 ===")
            word_success = self._generate_word_document()
            
            # 步骤4: 生成PPT演示文稿（如果启用）
            ppt_success = True
            if self.config['ppt']['generate']:
                self.logger.info("=== 步骤4: 生成PPT演示文稿 ===")
                ppt_success = self._generate_ppt_presentation()
            
            self.end_processing()
            
            # 生成处理报告
            self._generate_processing_summary()
            
            if word_success and ppt_success:
                return True, "报告生成成功"
            elif word_success:
                return True, "Word报告生成成功，PPT生成失败"
            else:
                return False, "报告生成失败"
                
        except Exception as e:
            self.handle_error(e, "报告生成")
            return False, f"报告生成过程中出现错误: {str(e)}"
        finally:
            self.cleanup()
    
    def _scan_and_classify_files(self) -> bool:
        """扫描和分类文件"""
        try:
            # 扫描文件
            file_map = self.scan_files()
            self.processed_data['file_map'] = file_map
            
            # 统计信息
            total_files = sum(len(files) for files in file_map.values())
            self.logger.info(f"文件扫描完成，共找到 {total_files} 个文件")
            
            for section_name, files in file_map.items():
                self.logger.info(f"  {section_name}: {len(files)} 个文件")
            
            return True
            
        except Exception as e:
            self.handle_error(e, "文件扫描")
            return False
    
    def _process_files(self) -> bool:
        """处理所有文件"""
        try:
            # 处理章节文件
            for section_name, files in self.processed_data['file_map'].items():
                self.logger.info(f"处理章节: {section_name}")
                
                section_data = {
                    'name': section_name,
                    'files': []
                }
                
                for file_path in files:
                    try:
                        # 验证文件
                        is_valid, validation_msg = self.validate_file(file_path)
                        if not is_valid:
                            self.logger.warning(f"文件验证失败 {file_path.name}: {validation_msg}")
                            continue
                        
                        # 处理文件
                        file_data = self._process_single_file(file_path)
                        if file_data:
                            section_data['files'].append(file_data)
                            self.processing_stats['files_processed'] += 1
                        
                    except Exception as e:
                        self.handle_error(e, f"处理文件 {file_path.name}")
                        continue
                
                self.processed_data['sections'][section_name] = section_data
                self.processing_stats['sections_completed'] += 1
            
            # 处理附录文件
            self._process_appendix_files()
            
            return True
            
        except Exception as e:
            self.handle_error(e, "文件处理")
            return False
    
    def _process_single_file(self, file_path: Path) -> Optional[Dict[str, Any]]:
        """处理单个文件"""
        suffix = file_path.suffix.lower()
        
        try:
            if suffix in ['.xlsx', '.xls']:
                return self.file_processor.process_excel_file(file_path)
            elif suffix in ['.png', '.jpg', '.jpeg', '.gif', '.bmp']:
                return self.file_processor.process_image_file(file_path)
            elif suffix in ['.txt', '.csv', '.md']:
                return self.file_processor.process_text_file(file_path)
            elif suffix in ['.py', '.sql', '.r', '.sas']:
                return self.file_processor.process_code_file(file_path)
            else:
                self.logger.warning(f"不支持的文件类型: {file_path.name}")
                return None
                
        except Exception as e:
            self.logger.error(f"处理文件失败 {file_path.name}: {str(e)}")
            return None
    
    def _process_appendix_files(self):
        """处理附录文件"""
        if not self.config.get('appendix'):
            return
        
        self.logger.info("处理附录文件")
        
        report_dir = Path(self.config['paths']['report_dir'])
        
        for appendix_item in self.config['appendix']:
            item_name = appendix_item['name']
            self.logger.info(f"处理附录项: {item_name}")
            
            appendix_files = []
            
            for pattern in appendix_item['file_patterns']:
                import re
                for file_path in report_dir.rglob('*'):
                    if file_path.is_file() and re.search(pattern, file_path.name, re.IGNORECASE):
                        file_data = self._process_single_file(file_path)
                        if file_data:
                            appendix_files.append(file_data)
            
            self.processed_data['appendix'][item_name] = appendix_files
    
    def _generate_word_document(self) -> bool:
        """生成Word文档"""
        try:
            # 添加封面页
            self.word_generator.add_cover_page()
            
            # 添加目录
            self.word_generator.add_table_of_contents()
            
            # 添加各章节
            for section_name, section_data in self.processed_data['sections'].items():
                if section_data['files']:  # 只添加有内容的章节
                    self.word_generator.add_section(section_name, section_data)
            
            # 添加附录
            if self.processed_data['appendix']:
                self.word_generator.add_appendix(self.processed_data['appendix'])
            
            # 保存文档
            output_path = self.output_dir / f"{self.config['document']['title']}.docx"
            return self.word_generator.save_document(output_path)
            
        except Exception as e:
            self.handle_error(e, "Word文档生成")
            return False
    
    def _generate_ppt_presentation(self) -> bool:
        """生成PPT演示文稿"""
        if not self.ppt_generator:
            return True
        
        try:
            # 添加标题幻灯片
            self.ppt_generator.add_title_slide()
            
            # 添加议程幻灯片
            self.ppt_generator.add_agenda_slide()
            
            # 添加各章节幻灯片
            for section_name, section_data in self.processed_data['sections'].items():
                if section_data['files']:  # 只添加有内容的章节
                    self.ppt_generator.add_section_slides(section_name, section_data)
            
            # 添加总结幻灯片
            self.ppt_generator.add_summary_slide()
            
            # 添加致谢幻灯片
            self.ppt_generator.add_thank_you_slide()
            
            # 保存演示文稿
            output_path = self.output_dir / f"{self.config['document']['title']}.pptx"
            return self.ppt_generator.save_presentation(output_path)
            
        except Exception as e:
            self.handle_error(e, "PPT演示文稿生成")
            return False
    
    def _generate_processing_summary(self):
        """生成处理摘要"""
        summary = {
            'processing_time': self.processing_stats['end_time'] - self.processing_stats['start_time'],
            'files_processed': self.processing_stats['files_processed'],
            'sections_completed': self.processing_stats['sections_completed'],
            'errors_count': self.processing_stats['errors_count'],
            'output_files': []
        }
        
        # 检查输出文件
        word_file = self.output_dir / f"{self.config['document']['title']}.docx"
        if word_file.exists():
            summary['output_files'].append(str(word_file))
        
        if self.config['ppt']['generate']:
            ppt_file = self.output_dir / f"{self.config['document']['title']}.pptx"
            if ppt_file.exists():
                summary['output_files'].append(str(ppt_file))
        
        self.processed_data['processing_summary'] = summary
        
        # 记录摘要
        self.logger.info("=== 处理摘要 ===")
        self.logger.info(f"处理时间: {summary['processing_time']}")
        self.logger.info(f"处理文件数: {summary['files_processed']}")
        self.logger.info(f"完成章节数: {summary['sections_completed']}")
        self.logger.info(f"错误数量: {summary['errors_count']}")
        self.logger.info(f"输出文件: {summary['output_files']}")
    
    def generate_section_only(self, section_name: str) -> Tuple[bool, str]:
        """
        仅生成指定章节
        
        Args:
            section_name: 章节名称
            
        Returns:
            (成功标志, 结果消息)
        """
        try:
            self.logger.info(f"生成单个章节: {section_name}")
            
            # 扫描文件
            file_map = self.scan_files()
            
            if section_name not in file_map:
                return False, f"未找到章节: {section_name}"
            
            # 处理指定章节的文件
            files = file_map[section_name]
            section_data = {
                'name': section_name,
                'files': []
            }
            
            for file_path in files:
                file_data = self._process_single_file(file_path)
                if file_data:
                    section_data['files'].append(file_data)
            
            # 创建临时Word文档
            temp_generator = WordGenerator(self.config, self.logger)
            temp_generator.add_section(section_name, section_data)
            
            # 保存临时文档
            output_path = self.output_dir / f"{section_name}_临时报告.docx"
            success = temp_generator.save_document(output_path)
            
            if success:
                return True, f"章节 '{section_name}' 生成成功: {output_path}"
            else:
                return False, f"章节 '{section_name}' 生成失败"
                
        except Exception as e:
            self.handle_error(e, f"生成章节 {section_name}")
            return False, f"生成章节时出现错误: {str(e)}"
    
    def list_available_sections(self) -> List[str]:
        """列出可用的章节"""
        try:
            file_map = self.scan_files()
            return [name for name, files in file_map.items() if files]
        except Exception as e:
            self.logger.error(f"列出章节失败: {str(e)}")
            return []
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        return self.processing_stats.copy()
    
    def get_processed_data(self) -> Dict[str, Any]:
        """获取处理后的数据"""
        return self.processed_data.copy()


def main():
    """主函数 - 命令行接口"""
    import argparse
    
    parser = argparse.ArgumentParser(description='增强版报告生成器')
    parser.add_argument('--config', '-c', help='配置文件路径')
    parser.add_argument('--work-dir', '-w', help='工作目录')
    parser.add_argument('--section', '-s', help='仅生成指定章节')
    parser.add_argument('--list-sections', '-l', action='store_true', help='列出可用章节')
    parser.add_argument('--interactive', '-i', action='store_true', help='交互式模式')
    
    args = parser.parse_args()
    
    try:
        # 创建生成器
        generator = EnhancedReportGenerator(
            config_path=args.config,
            work_dir=args.work_dir
        )
        
        if args.list_sections:
            # 列出可用章节
            sections = generator.list_available_sections()
            print("可用章节:")
            for i, section in enumerate(sections, 1):
                print(f"  {i}. {section}")
            return
        
        if args.section:
            # 生成指定章节
            success, message = generator.generate_section_only(args.section)
            print(message)
            return
        
        if args.interactive:
            # 交互式模式
            print("=== 增强版报告生成器 ===")
            print("1. 生成完整报告")
            print("2. 生成指定章节")
            print("3. 列出可用章节")
            
            choice = input("请选择操作 (1-3): ").strip()
            
            if choice == '1':
                success, message = generator.generate_report()
                print(message)
            elif choice == '2':
                sections = generator.list_available_sections()
                print("可用章节:")
                for i, section in enumerate(sections, 1):
                    print(f"  {i}. {section}")
                
                section_choice = input("请输入章节编号或名称: ").strip()
                if section_choice.isdigit():
                    section_idx = int(section_choice) - 1
                    if 0 <= section_idx < len(sections):
                        section_name = sections[section_idx]
                    else:
                        print("无效的章节编号")
                        return
                else:
                    section_name = section_choice
                
                success, message = generator.generate_section_only(section_name)
                print(message)
            elif choice == '3':
                sections = generator.list_available_sections()
                print("可用章节:")
                for i, section in enumerate(sections, 1):
                    print(f"  {i}. {section}")
            else:
                print("无效的选择")
        else:
            # 默认生成完整报告
            success, message = generator.generate_report()
            print(message)
            
    except Exception as e:
        print(f"程序执行失败: {str(e)}")


if __name__ == '__main__':
    main()
