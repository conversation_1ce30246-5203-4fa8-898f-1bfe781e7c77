"""
PowerPoint演示文稿生成模块
PowerPoint Presentation Generator Module

基于Word文档内容生成对应的PPT演示文稿
"""

import io
import os
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime
import logging

from pptx import Presentation
from pptx.util import Inches, Cm, Pt
from pptx.enum.text import PP_ALIGN
from pptx.dml.color import RGBColor
from pptx.enum.shapes import MSO_SHAPE


class PPTGenerator:
    """PowerPoint生成器"""
    
    def __init__(self, config: Dict[str, Any], logger: logging.Logger):
        """
        初始化PPT生成器
        
        Args:
            config: 配置字典
            logger: 日志记录器
        """
        self.config = config
        self.logger = logger
        self.ppt = Presentation()
        self.font_config = self._get_font_config()
        
        # 设置幻灯片尺寸
        self._setup_slide_size()
        
        # 颜色主题
        self.colors = {
            'primary': RGBColor(68, 114, 196),      # 蓝色
            'secondary': RGBColor(112, 173, 71),    # 绿色
            'accent': RGBColor(255, 192, 0),        # 橙色
            'text': RGBColor(64, 64, 64),           # 深灰色
            'light_gray': RGBColor(242, 242, 242),  # 浅灰色
            'white': RGBColor(255, 255, 255)        # 白色
        }
    
    def _get_font_config(self) -> Dict[str, str]:
        """获取字体配置"""
        import matplotlib.font_manager as fm
        available_fonts = {f.name for f in fm.fontManager.ttflist}
        font_config = self.config['styling']['fonts']
        
        # 选择主字体
        main_font = font_config['fallback']
        for font in font_config['priority']:
            if font in available_fonts:
                main_font = font
                break
        
        return {
            'main_font': main_font,
            'code_font': font_config['code_font'][0]
        }
    
    def _setup_slide_size(self):
        """设置幻灯片尺寸"""
        ppt_config = self.config['ppt']
        
        # 设置幻灯片尺寸
        slide_width = Cm(ppt_config['dimensions']['width'])
        slide_height = Cm(ppt_config['dimensions']['height'])
        
        self.ppt.slide_width = slide_width
        self.ppt.slide_height = slide_height
    
    def add_title_slide(self):
        """添加标题幻灯片"""
        self.logger.info("添加标题幻灯片")
        
        # 使用标题幻灯片布局
        title_slide_layout = self.ppt.slide_layouts[self.config['ppt']['layouts']['title_slide']]
        slide = self.ppt.slides.add_slide(title_slide_layout)
        
        doc_config = self.config['document']
        
        # 设置标题
        title = slide.shapes.title
        title.text = doc_config['title']
        
        # 设置标题样式
        title_font = title.text_frame.paragraphs[0].font
        title_font.name = self.font_config['main_font']
        title_font.size = Pt(self.config['ppt']['styles']['title_font_size'])
        title_font.bold = True
        title_font.color.rgb = self.colors['primary']
        
        # 设置副标题
        if hasattr(slide.placeholders, '__len__') and len(slide.placeholders) > 1:
            subtitle = slide.placeholders[1]
            subtitle_text = []
            
            if doc_config.get('subtitle'):
                subtitle_text.append(doc_config['subtitle'])
            
            subtitle_text.extend([
                f"版本: {doc_config['version']}",
                f"作者: {doc_config['author']}",
                f"公司: {doc_config['company']}",
                f"日期: {datetime.now().strftime('%Y年%m月%d日')}"
            ])
            
            subtitle.text = '\n'.join(subtitle_text)
            
            # 设置副标题样式
            for paragraph in subtitle.text_frame.paragraphs:
                paragraph.font.name = self.font_config['main_font']
                paragraph.font.size = Pt(self.config['ppt']['styles']['content_font_size'])
                paragraph.font.color.rgb = self.colors['text']
                paragraph.alignment = PP_ALIGN.CENTER
    
    def add_agenda_slide(self):
        """添加议程幻灯片"""
        self.logger.info("添加议程幻灯片")
        
        # 使用内容幻灯片布局
        content_slide_layout = self.ppt.slide_layouts[self.config['ppt']['layouts']['content_slide']]
        slide = self.ppt.slides.add_slide(content_slide_layout)
        
        # 设置标题
        title = slide.shapes.title
        title.text = "报告内容"
        self._set_title_style(title)
        
        # 添加内容
        content = slide.placeholders[1]
        text_frame = content.text_frame
        text_frame.clear()
        
        # 添加章节列表
        for i, section in enumerate(self.config['sections'], 1):
            p = text_frame.paragraphs[0] if i == 1 else text_frame.add_paragraph()
            p.text = f"{i}. {section['name']}"
            p.level = 0
            
            # 设置样式
            p.font.name = self.font_config['main_font']
            p.font.size = Pt(self.config['ppt']['styles']['bullet_font_size'])
            p.font.color.rgb = self.colors['text']
            
            # 添加描述
            if section.get('description'):
                desc_p = text_frame.add_paragraph()
                desc_p.text = section['description']
                desc_p.level = 1
                desc_p.font.name = self.font_config['main_font']
                desc_p.font.size = Pt(self.config['ppt']['styles']['bullet_font_size'] - 2)
                desc_p.font.color.rgb = self.colors['text']
                desc_p.font.italic = True
    
    def add_section_slides(self, section_name: str, section_data: Dict[str, Any]):
        """
        添加章节幻灯片
        
        Args:
            section_name: 章节名称
            section_data: 章节数据
        """
        self.logger.info(f"添加章节幻灯片: {section_name}")
        
        # 添加章节标题幻灯片
        self._add_section_title_slide(section_name)
        
        # 处理章节中的文件
        if 'files' in section_data:
            for file_data in section_data['files']:
                if file_data.get('success', False):
                    self._add_file_slide(file_data, section_name)
    
    def _add_section_title_slide(self, section_name: str):
        """添加章节标题幻灯片"""
        content_slide_layout = self.ppt.slide_layouts[self.config['ppt']['layouts']['content_slide']]
        slide = self.ppt.slides.add_slide(content_slide_layout)
        
        # 设置标题
        title = slide.shapes.title
        title.text = section_name
        self._set_title_style(title)
        
        # 添加章节描述
        section_config = next((s for s in self.config['sections'] if s['name'] == section_name), None)
        if section_config and section_config.get('description'):
            content = slide.placeholders[1]
            content.text = section_config['description']
            
            # 设置内容样式
            for paragraph in content.text_frame.paragraphs:
                paragraph.font.name = self.font_config['main_font']
                paragraph.font.size = Pt(self.config['ppt']['styles']['content_font_size'])
                paragraph.font.color.rgb = self.colors['text']
                paragraph.alignment = PP_ALIGN.CENTER
    
    def _add_file_slide(self, file_data: Dict[str, Any], section_name: str):
        """添加文件内容幻灯片"""
        file_type = file_data.get('file_type')
        file_path = file_data.get('file_path')
        
        if file_type == 'excel':
            self._add_excel_slide(file_data, section_name)
        elif file_type == 'image':
            self._add_image_slide(file_data, section_name)
        elif file_type in ['text', 'code']:
            self._add_text_slide(file_data, section_name)
    
    def _add_excel_slide(self, file_data: Dict[str, Any], section_name: str):
        """添加Excel内容幻灯片"""
        if file_data.get('images'):
            # 为每个Excel图片创建幻灯片
            for img_data in file_data['images']:
                self._create_image_slide(
                    img_data['image_data'],
                    img_data.get('caption', ''),
                    section_name
                )
    
    def _add_image_slide(self, file_data: Dict[str, Any], section_name: str):
        """添加图片幻灯片"""
        if file_data.get('image_data'):
            caption = file_data.get('caption', '')
            self._create_image_slide(file_data['image_data'], caption, section_name)
    
    def _add_text_slide(self, file_data: Dict[str, Any], section_name: str):
        """添加文本内容幻灯片"""
        content = file_data.get('content', '')
        file_path = file_data.get('file_path')
        
        if not content:
            return
        
        # 使用内容幻灯片布局
        content_slide_layout = self.ppt.slide_layouts[self.config['ppt']['layouts']['content_slide']]
        slide = self.ppt.slides.add_slide(content_slide_layout)
        
        # 设置标题
        title = slide.shapes.title
        title.text = file_path.name if file_path else "文本内容"
        self._set_title_style(title)
        
        # 添加内容
        content_placeholder = slide.placeholders[1]
        
        # 限制文本长度
        max_chars = 800
        if len(content) > max_chars:
            content = content[:max_chars] + "\n\n... (内容已截断)"
        
        content_placeholder.text = content
        
        # 设置文本样式
        for paragraph in content_placeholder.text_frame.paragraphs:
            if file_data.get('file_type') == 'code':
                paragraph.font.name = self.font_config['code_font']
                paragraph.font.size = Pt(self.config['ppt']['styles']['bullet_font_size'] - 2)
            else:
                paragraph.font.name = self.font_config['main_font']
                paragraph.font.size = Pt(self.config['ppt']['styles']['bullet_font_size'])
            
            paragraph.font.color.rgb = self.colors['text']
    
    def _create_image_slide(self, image_bytes: bytes, caption: str, section_name: str):
        """创建图片幻灯片"""
        try:
            # 使用空白布局
            blank_slide_layout = self.ppt.slide_layouts[self.config['ppt']['layouts']['blank']]
            slide = self.ppt.slides.add_slide(blank_slide_layout)
            
            # 添加标题文本框
            title_left = Inches(0.5)
            title_top = Inches(0.2)
            title_width = Inches(9)
            title_height = Inches(0.8)
            
            title_box = slide.shapes.add_textbox(title_left, title_top, title_width, title_height)
            title_frame = title_box.text_frame
            title_frame.text = caption if caption else "图表"
            
            # 设置标题样式
            title_paragraph = title_frame.paragraphs[0]
            title_paragraph.font.name = self.font_config['main_font']
            title_paragraph.font.size = Pt(self.config['ppt']['styles']['title_font_size'] - 4)
            title_paragraph.font.bold = True
            title_paragraph.font.color.rgb = self.colors['primary']
            title_paragraph.alignment = PP_ALIGN.CENTER
            
            # 添加图片
            img_stream = io.BytesIO(image_bytes)
            
            # 计算图片位置和大小
            img_left = Inches(0.5)
            img_top = Inches(1.2)
            img_width = Inches(9)
            
            picture = slide.shapes.add_picture(img_stream, img_left, img_top, width=img_width)
            
            # 添加页脚（章节名称）
            footer_left = Inches(0.5)
            footer_top = Inches(6.8)
            footer_width = Inches(9)
            footer_height = Inches(0.3)
            
            footer_box = slide.shapes.add_textbox(footer_left, footer_top, footer_width, footer_height)
            footer_frame = footer_box.text_frame
            footer_frame.text = section_name
            
            footer_paragraph = footer_frame.paragraphs[0]
            footer_paragraph.font.name = self.font_config['main_font']
            footer_paragraph.font.size = Pt(12)
            footer_paragraph.font.color.rgb = self.colors['text']
            footer_paragraph.alignment = PP_ALIGN.RIGHT
            
        except Exception as e:
            self.logger.error(f"创建图片幻灯片失败: {str(e)}")
    
    def _set_title_style(self, title_shape):
        """设置标题样式"""
        title_font = title_shape.text_frame.paragraphs[0].font
        title_font.name = self.font_config['main_font']
        title_font.size = Pt(self.config['ppt']['styles']['title_font_size'] - 4)
        title_font.bold = True
        title_font.color.rgb = self.colors['primary']
    
    def add_summary_slide(self):
        """添加总结幻灯片"""
        self.logger.info("添加总结幻灯片")
        
        content_slide_layout = self.ppt.slide_layouts[self.config['ppt']['layouts']['content_slide']]
        slide = self.ppt.slides.add_slide(content_slide_layout)
        
        # 设置标题
        title = slide.shapes.title
        title.text = "报告总结"
        self._set_title_style(title)
        
        # 添加总结内容
        content = slide.placeholders[1]
        text_frame = content.text_frame
        text_frame.clear()
        
        summary_points = [
            "本报告详细记录了机器学习建模的完整过程",
            "包含数据准备、特征工程、模型构建和评估等关键环节",
            "提供了模型部署所需的评分代码和相关材料",
            "为后续模型优化和业务应用提供了重要参考"
        ]
        
        for i, point in enumerate(summary_points):
            p = text_frame.paragraphs[0] if i == 0 else text_frame.add_paragraph()
            p.text = point
            p.level = 0
            
            # 设置样式
            p.font.name = self.font_config['main_font']
            p.font.size = Pt(self.config['ppt']['styles']['bullet_font_size'])
            p.font.color.rgb = self.colors['text']
    
    def add_thank_you_slide(self):
        """添加致谢幻灯片"""
        self.logger.info("添加致谢幻灯片")
        
        # 使用标题幻灯片布局
        title_slide_layout = self.ppt.slide_layouts[self.config['ppt']['layouts']['title_slide']]
        slide = self.ppt.slides.add_slide(title_slide_layout)
        
        # 设置标题
        title = slide.shapes.title
        title.text = "谢谢！"
        
        # 设置标题样式
        title_font = title.text_frame.paragraphs[0].font
        title_font.name = self.font_config['main_font']
        title_font.size = Pt(self.config['ppt']['styles']['title_font_size'] + 8)
        title_font.bold = True
        title_font.color.rgb = self.colors['primary']
        
        # 设置副标题
        if hasattr(slide.placeholders, '__len__') and len(slide.placeholders) > 1:
            subtitle = slide.placeholders[1]
            subtitle.text = "Questions & Discussion\n问题与讨论"
            
            # 设置副标题样式
            for paragraph in subtitle.text_frame.paragraphs:
                paragraph.font.name = self.font_config['main_font']
                paragraph.font.size = Pt(self.config['ppt']['styles']['content_font_size'])
                paragraph.font.color.rgb = self.colors['text']
                paragraph.alignment = PP_ALIGN.CENTER
    
    def save_presentation(self, output_path: Path) -> bool:
        """
        保存演示文稿
        
        Args:
            output_path: 输出路径
            
        Returns:
            是否保存成功
        """
        try:
            self.logger.info(f"保存PPT演示文稿到: {output_path}")
            
            # 确保输出目录存在
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 保存演示文稿
            self.ppt.save(str(output_path))
            
            self.logger.info("PPT演示文稿保存成功")
            return True
            
        except Exception as e:
            self.logger.error(f"保存PPT演示文稿失败: {str(e)}")
            return False
