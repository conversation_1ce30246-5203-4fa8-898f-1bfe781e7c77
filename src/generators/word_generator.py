"""
Word文档生成模块
Word Document Generator Module

生成专业的Word技术报告文档
"""

import io
import os
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime
import logging

from docx import Document
from docx.shared import Cm, Pt, Inches
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.table import WD_TABLE_ALIGNMENT
from docx.oxml.shared import OxmlElement, qn
from docx.oxml.ns import nsdecls
from docx.oxml import parse_xml


class WordGenerator:
    """Word文档生成器"""
    
    def __init__(self, config: Dict[str, Any], logger: logging.Logger):
        """
        初始化Word生成器
        
        Args:
            config: 配置字典
            logger: 日志记录器
        """
        self.config = config
        self.logger = logger
        self.doc = Document()
        self.font_config = self._get_font_config()
        
        # 设置文档样式
        self._setup_document_styles()
    
    def _get_font_config(self) -> Dict[str, str]:
        """获取字体配置"""
        import matplotlib.font_manager as fm
        available_fonts = {f.name for f in fm.fontManager.ttflist}
        font_config = self.config['styling']['fonts']
        
        # 选择主字体
        main_font = font_config['fallback']
        for font in font_config['priority']:
            if font in available_fonts:
                main_font = font
                break
        
        return {
            'main_font': main_font,
            'code_font': font_config['code_font'][0]
        }
    
    def _setup_document_styles(self):
        """设置文档样式"""
        # 设置默认样式
        style = self.doc.styles['Normal']
        font = style.font
        font.name = self.font_config['main_font']
        font.size = Pt(self.config['styling']['font_sizes']['normal'])
        
        # 设置段落格式
        paragraph_format = style.paragraph_format
        paragraph_format.line_spacing = self.config['styling']['word_styles']['line_spacing']
        paragraph_format.space_before = Pt(self.config['styling']['word_styles']['paragraph_spacing_before'])
        paragraph_format.space_after = Pt(self.config['styling']['word_styles']['paragraph_spacing_after'])
        
        # 设置页面边距
        sections = self.doc.sections
        for section in sections:
            section.top_margin = Cm(self.config['styling']['page']['margins']['top'])
            section.bottom_margin = Cm(self.config['styling']['page']['margins']['bottom'])
            section.left_margin = Cm(self.config['styling']['page']['margins']['left'])
            section.right_margin = Cm(self.config['styling']['page']['margins']['right'])
        
        # 创建自定义样式
        self._create_custom_styles()
    
    def _create_custom_styles(self):
        """创建自定义样式"""
        styles = self.doc.styles
        
        # 代码样式
        try:
            code_style = styles.add_style('Code', 1)  # 1 = paragraph style
            code_font = code_style.font
            code_font.name = self.font_config['code_font']
            code_font.size = Pt(self.config['styling']['font_sizes']['code'])
            
            # 设置代码块背景色
            code_paragraph = code_style.paragraph_format
            code_paragraph.left_indent = Cm(0.5)
            code_paragraph.right_indent = Cm(0.5)
            code_paragraph.space_before = Pt(6)
            code_paragraph.space_after = Pt(6)
            
        except Exception as e:
            self.logger.warning(f"创建代码样式失败: {str(e)}")
        
        # 图片标题样式
        try:
            caption_style = styles.add_style('Caption', 1)
            caption_font = caption_style.font
            caption_font.name = self.font_config['main_font']
            caption_font.size = Pt(self.config['styling']['font_sizes']['caption'])
            caption_font.italic = True
            
            caption_paragraph = caption_style.paragraph_format
            caption_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
            caption_paragraph.space_before = Pt(3)
            caption_paragraph.space_after = Pt(6)
            
        except Exception as e:
            self.logger.warning(f"创建标题样式失败: {str(e)}")
    
    def add_cover_page(self):
        """添加封面页"""
        self.logger.info("添加封面页")
        
        doc_config = self.config['document']
        
        # 主标题
        title = self.doc.add_heading(doc_config['title'], 0)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 副标题
        if doc_config.get('subtitle'):
            subtitle = self.doc.add_heading(doc_config['subtitle'], 1)
            subtitle.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 添加空行
        self.doc.add_paragraph()
        self.doc.add_paragraph()
        
        # 项目信息
        info_table = self.doc.add_table(rows=5, cols=2)
        info_table.style = 'Table Grid'
        info_table.alignment = WD_TABLE_ALIGNMENT.CENTER
        
        # 设置表格内容
        info_data = [
            ('项目名称', doc_config.get('project_name', doc_config['title'])),
            ('版本', str(doc_config['version'])),
            ('作者', doc_config['author']),
            ('公司', doc_config['company']),
            ('生成日期', datetime.now().strftime('%Y年%m月%d日'))
        ]
        
        for i, (label, value) in enumerate(info_data):
            row = info_table.rows[i]
            row.cells[0].text = label
            row.cells[1].text = value
            
            # 设置单元格样式
            for cell in row.cells:
                cell.paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER
                for run in cell.paragraphs[0].runs:
                    run.font.name = self.font_config['main_font']
                    run.font.size = Pt(self.config['styling']['font_sizes']['normal'])
        
        # 设置表格列宽
        for row in info_table.rows:
            row.cells[0].width = Cm(4)
            row.cells[1].width = Cm(8)
        
        # 添加分页符
        self.doc.add_page_break()
    
    def add_table_of_contents(self):
        """添加目录"""
        self.logger.info("添加目录")
        
        # 目录标题
        toc_heading = self.doc.add_heading('目录', level=1)
        toc_heading.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 添加目录项
        for i, section in enumerate(self.config['sections'], 1):
            toc_item = self.doc.add_paragraph()
            toc_item.add_run(f"{i}. {section['name']}")
            toc_item.style = 'List Number'
            
            # 添加描述（如果有）
            if section.get('description'):
                desc_para = self.doc.add_paragraph()
                desc_run = desc_para.add_run(f"    {section['description']}")
                desc_run.font.size = Pt(self.config['styling']['font_sizes']['caption'])
                desc_run.font.italic = True
        
        # 添加附录到目录
        if self.config.get('appendix'):
            self.doc.add_paragraph()
            appendix_heading = self.doc.add_paragraph()
            appendix_heading.add_run("附录")
            appendix_heading.style = 'List Number'
            
            for appendix_item in self.config['appendix']:
                appendix_para = self.doc.add_paragraph()
                appendix_para.add_run(f"    {appendix_item['name']}")
                appendix_para.style = 'List Bullet'
        
        # 添加分页符
        self.doc.add_page_break()
    
    def add_section(self, section_name: str, section_data: Dict[str, Any]):
        """
        添加章节内容
        
        Args:
            section_name: 章节名称
            section_data: 章节数据
        """
        self.logger.info(f"添加章节: {section_name}")
        
        # 添加章节标题
        section_heading = self.doc.add_heading(section_name, level=1)
        
        # 添加章节描述
        section_config = next((s for s in self.config['sections'] if s['name'] == section_name), None)
        if section_config and section_config.get('description'):
            desc_para = self.doc.add_paragraph(section_config['description'])
            desc_para.style = 'Normal'
            for run in desc_para.runs:
                run.font.italic = True
        
        # 处理章节中的文件
        if 'files' in section_data:
            for file_data in section_data['files']:
                self._add_file_content(file_data)
        
        # 添加章节间距
        self.doc.add_paragraph()
    
    def _add_file_content(self, file_data: Dict[str, Any]):
        """添加文件内容到文档"""
        if not file_data.get('success', False):
            self.logger.warning(f"跳过失败的文件: {file_data.get('file_path', 'Unknown')}")
            return
        
        file_type = file_data.get('file_type')
        file_path = file_data.get('file_path')
        
        # 添加文件标题
        if file_path:
            file_title = self.doc.add_heading(file_path.name, level=3)
        
        if file_type == 'excel':
            self._add_excel_content(file_data)
        elif file_type == 'image':
            self._add_image_content(file_data)
        elif file_type == 'text':
            self._add_text_content(file_data)
        elif file_type == 'code':
            self._add_code_content(file_data)
        else:
            self.logger.warning(f"未知文件类型: {file_type}")
    
    def _add_excel_content(self, file_data: Dict[str, Any]):
        """添加Excel内容"""
        if file_data.get('images'):
            # 添加Excel转换的图片
            for img_data in file_data['images']:
                self._add_image_from_bytes(
                    img_data['image_data'],
                    img_data.get('caption', ''),
                    width=Cm(self.config['styling']['dimensions']['table_width'])
                )
        elif file_data.get('data'):
            # 添加Excel数据表格
            for sheet_name, df in file_data['data'].items():
                if not df.empty:
                    self._add_dataframe_table(df, f"表格: {sheet_name}")
    
    def _add_image_content(self, file_data: Dict[str, Any]):
        """添加图片内容"""
        if file_data.get('image_data'):
            caption = file_data.get('caption', '')
            self._add_image_from_bytes(
                file_data['image_data'],
                caption,
                width=Cm(self.config['styling']['dimensions']['image_width'])
            )
    
    def _add_text_content(self, file_data: Dict[str, Any]):
        """添加文本内容"""
        content = file_data.get('content', '')
        if content:
            para = self.doc.add_paragraph(content)
            para.style = 'Normal'
    
    def _add_code_content(self, file_data: Dict[str, Any]):
        """添加代码内容"""
        content = file_data.get('content', '')
        language = file_data.get('language', 'text')
        
        if content:
            # 添加代码语言标识
            lang_para = self.doc.add_paragraph(f"代码语言: {language.upper()}")
            for run in lang_para.runs:
                run.font.bold = True
                run.font.size = Pt(self.config['styling']['font_sizes']['caption'])
            
            # 添加代码内容
            code_para = self.doc.add_paragraph(content)
            try:
                code_para.style = 'Code'
            except:
                # 如果自定义样式不可用，使用默认样式
                for run in code_para.runs:
                    run.font.name = self.font_config['code_font']
                    run.font.size = Pt(self.config['styling']['font_sizes']['code'])
    
    def _add_image_from_bytes(self, image_bytes: bytes, caption: str = "", width: Cm = None):
        """从字节数据添加图片"""
        try:
            # 添加图片
            img_stream = io.BytesIO(image_bytes)
            
            if width:
                picture = self.doc.add_picture(img_stream, width=width)
            else:
                picture = self.doc.add_picture(img_stream)
            
            # 居中对齐
            last_paragraph = self.doc.paragraphs[-1]
            last_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            # 添加图片标题
            if caption:
                caption_para = self.doc.add_paragraph(f"图: {caption}")
                try:
                    caption_para.style = 'Caption'
                except:
                    caption_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
                    for run in caption_para.runs:
                        run.font.size = Pt(self.config['styling']['font_sizes']['caption'])
                        run.font.italic = True
            
        except Exception as e:
            self.logger.error(f"添加图片失败: {str(e)}")
            # 添加错误提示
            error_para = self.doc.add_paragraph(f"[图片加载失败: {caption}]")
            for run in error_para.runs:
                run.font.italic = True
                run.font.color.rgb = None  # 设置为灰色
    
    def _add_dataframe_table(self, df, title: str = ""):
        """添加DataFrame表格"""
        try:
            if title:
                title_para = self.doc.add_paragraph(title)
                for run in title_para.runs:
                    run.font.bold = True
            
            # 创建表格
            table = self.doc.add_table(rows=1, cols=len(df.columns))
            table.style = self.config['styling']['word_styles']['table_style']
            
            # 添加表头
            hdr_cells = table.rows[0].cells
            for i, col in enumerate(df.columns):
                hdr_cells[i].text = str(col)
                # 设置表头样式
                for paragraph in hdr_cells[i].paragraphs:
                    for run in paragraph.runs:
                        run.font.bold = True
            
            # 添加数据行（限制行数）
            max_rows = 20
            df_display = df.head(max_rows) if len(df) > max_rows else df
            
            for _, row in df_display.iterrows():
                row_cells = table.add_row().cells
                for i, val in enumerate(row):
                    row_cells[i].text = str(val) if val is not None else ""
            
            # 如果数据被截断，添加说明
            if len(df) > max_rows:
                note_para = self.doc.add_paragraph(f"注: 表格共{len(df)}行，仅显示前{max_rows}行")
                for run in note_para.runs:
                    run.font.size = Pt(self.config['styling']['font_sizes']['caption'])
                    run.font.italic = True
            
        except Exception as e:
            self.logger.error(f"添加表格失败: {str(e)}")
    
    def add_appendix(self, appendix_data: Dict[str, Any]):
        """添加附录"""
        self.logger.info("添加附录")
        
        # 添加附录标题
        appendix_heading = self.doc.add_heading('附录', level=1)
        
        # 处理附录内容
        for appendix_item in self.config.get('appendix', []):
            item_name = appendix_item['name']
            
            # 添加附录子标题
            self.doc.add_heading(item_name, level=2)
            
            # 添加对应的文件内容
            if item_name in appendix_data:
                for file_data in appendix_data[item_name]:
                    self._add_file_content(file_data)
    
    def save_document(self, output_path: Path) -> bool:
        """
        保存文档
        
        Args:
            output_path: 输出路径
            
        Returns:
            是否保存成功
        """
        try:
            self.logger.info(f"保存Word文档到: {output_path}")
            
            # 确保输出目录存在
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 保存文档
            self.doc.save(str(output_path))
            
            self.logger.info("Word文档保存成功")
            return True
            
        except Exception as e:
            self.logger.error(f"保存Word文档失败: {str(e)}")
            return False
