"""
文件处理模块
File Processing Module

处理各种类型的文件，包括Excel、图片、文本等
"""

import os
import io
import re
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, Union
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from PIL import Image, ImageDraw, ImageFont
import logging


class FileProcessor:
    """文件处理器"""
    
    def __init__(self, config: Dict[str, Any], logger: logging.Logger):
        """
        初始化文件处理器
        
        Args:
            config: 配置字典
            logger: 日志记录器
        """
        self.config = config
        self.logger = logger
        self.font_config = self._setup_fonts()
        
        # 设置matplotlib中文字体
        plt.rcParams['font.sans-serif'] = [self.font_config['main_font']]
        plt.rcParams['axes.unicode_minus'] = False
    
    def _setup_fonts(self) -> Dict[str, str]:
        """设置字体配置"""
        available_fonts = {f.name for f in fm.fontManager.ttflist}
        font_config = self.config['styling']['fonts']
        
        # 选择主字体
        main_font = font_config['fallback']
        for font in font_config['priority']:
            if font in available_fonts:
                main_font = font
                break
        
        # 选择代码字体
        code_font = font_config['code_font'][0]
        for font in font_config['code_font']:
            if font in available_fonts:
                code_font = font
                break
        
        return {
            'main_font': main_font,
            'code_font': code_font
        }
    
    def process_excel_file(self, file_path: Path, as_image: bool = None) -> Dict[str, Any]:
        """
        处理Excel文件
        
        Args:
            file_path: Excel文件路径
            as_image: 是否转换为图片
            
        Returns:
            处理结果字典
        """
        if as_image is None:
            as_image = self.config['styling']['processing']['excel_as_image']
        
        try:
            self.logger.info(f"处理Excel文件: {file_path.name}")
            
            # 读取Excel文件
            excel_data = self._read_excel_file(file_path)
            
            result = {
                'file_path': file_path,
                'file_type': 'excel',
                'success': True,
                'data': excel_data,
                'images': [],
                'error': None
            }
            
            if as_image:
                # 转换为图片
                for sheet_name, df in excel_data.items():
                    if not df.empty:
                        img_data = self._dataframe_to_image(df, f"{file_path.stem} - {sheet_name}")
                        result['images'].append({
                            'sheet_name': sheet_name,
                            'image_data': img_data,
                            'caption': f"{file_path.stem} - {sheet_name}"
                        })
            
            return result
            
        except Exception as e:
            self.logger.error(f"处理Excel文件失败 {file_path.name}: {str(e)}")
            return {
                'file_path': file_path,
                'file_type': 'excel',
                'success': False,
                'data': None,
                'images': [],
                'error': str(e)
            }
    
    def _read_excel_file(self, file_path: Path) -> Dict[str, pd.DataFrame]:
        """读取Excel文件的所有工作表"""
        handler_config = self.config['file_handlers']['excel']
        
        try:
            # 读取所有工作表
            excel_file = pd.ExcelFile(file_path)
            sheet_names = excel_file.sheet_names
            
            data = {}
            sheet_selection = handler_config.get('sheet_selection', 'auto')
            
            if sheet_selection == 'first':
                # 只读取第一个工作表
                df = pd.read_excel(file_path, sheet_name=sheet_names[0])
                data[sheet_names[0]] = df
                
            elif sheet_selection == 'specific':
                # 读取指定工作表
                specific_sheets = handler_config.get('specific_sheets', [])
                for sheet in specific_sheets:
                    if sheet in sheet_names:
                        df = pd.read_excel(file_path, sheet_name=sheet)
                        data[sheet] = df
                        
            else:  # 'auto' 或 'all'
                # 读取所有工作表，但限制预览行数
                max_rows = handler_config.get('max_rows_preview', 20)
                for sheet_name in sheet_names:
                    try:
                        df = pd.read_excel(file_path, sheet_name=sheet_name, nrows=max_rows)
                        if not df.empty:
                            data[sheet_name] = df
                    except Exception as e:
                        self.logger.warning(f"读取工作表 {sheet_name} 失败: {str(e)}")
                        continue
            
            return data
            
        except Exception as e:
            raise Exception(f"读取Excel文件失败: {str(e)}")
    
    def _dataframe_to_image(self, df: pd.DataFrame, title: str = "") -> bytes:
        """将DataFrame转换为图片"""
        try:
            # 限制显示的行数和列数
            max_rows = 20
            max_cols = 10
            
            if len(df) > max_rows:
                df_display = df.head(max_rows).copy()
                title += f" (显示前{max_rows}行，共{len(df)}行)"
            else:
                df_display = df.copy()
            
            if len(df.columns) > max_cols:
                df_display = df_display.iloc[:, :max_cols]
                title += f" (显示前{max_cols}列，共{len(df.columns)}列)"
            
            # 处理数据格式
            for col in df_display.columns:
                if df_display[col].dtype == 'object':
                    df_display[col] = df_display[col].astype(str)
                elif df_display[col].dtype in ['float64', 'float32']:
                    df_display[col] = df_display[col].round(4)
            
            # 创建图表
            fig, ax = plt.subplots(figsize=(12, max(6, len(df_display) * 0.4 + 2)))
            ax.axis('tight')
            ax.axis('off')
            
            if title:
                ax.set_title(title, fontsize=14, fontweight='bold', pad=20)
            
            # 创建表格
            table = ax.table(
                cellText=df_display.values,
                colLabels=df_display.columns,
                cellLoc='center',
                loc='center'
            )
            
            # 设置表格样式
            table.auto_set_font_size(False)
            table.set_fontsize(9)
            table.scale(1.2, 1.8)
            
            # 设置表头样式
            for i in range(len(df_display.columns)):
                table[(0, i)].set_facecolor('#4CAF50')
                table[(0, i)].set_text_props(weight='bold', color='white')
            
            # 设置交替行颜色
            for i in range(1, len(df_display) + 1):
                for j in range(len(df_display.columns)):
                    if i % 2 == 0:
                        table[(i, j)].set_facecolor('#f2f2f2')
            
            # 保存为字节流
            img_buffer = io.BytesIO()
            plt.savefig(img_buffer, format='png', dpi=300, bbox_inches='tight', 
                       facecolor='white', edgecolor='none')
            plt.close(fig)
            
            img_buffer.seek(0)
            return img_buffer.getvalue()
            
        except Exception as e:
            self.logger.error(f"DataFrame转图片失败: {str(e)}")
            raise
    
    def process_image_file(self, file_path: Path) -> Dict[str, Any]:
        """
        处理图片文件
        
        Args:
            file_path: 图片文件路径
            
        Returns:
            处理结果字典
        """
        try:
            self.logger.info(f"处理图片文件: {file_path.name}")
            
            # 打开并验证图片
            with Image.open(file_path) as img:
                # 获取图片信息
                img_info = {
                    'size': img.size,
                    'mode': img.mode,
                    'format': img.format
                }
                
                # 处理图片（调整大小、压缩等）
                processed_img = self._process_image(img)
                
                # 生成标题
                caption = self._generate_image_caption(file_path)
                
                return {
                    'file_path': file_path,
                    'file_type': 'image',
                    'success': True,
                    'image_data': processed_img,
                    'caption': caption,
                    'info': img_info,
                    'error': None
                }
                
        except Exception as e:
            self.logger.error(f"处理图片文件失败 {file_path.name}: {str(e)}")
            return {
                'file_path': file_path,
                'file_type': 'image',
                'success': False,
                'image_data': None,
                'caption': None,
                'info': None,
                'error': str(e)
            }
    
    def _process_image(self, img: Image.Image) -> bytes:
        """处理图片（调整大小、压缩等）"""
        processing_config = self.config['styling']['processing']
        
        # 复制图片以避免修改原图
        processed_img = img.copy()
        
        # 自动调整大小
        if processing_config.get('auto_resize_images', True):
            max_width = 1200
            max_height = 800
            
            if processed_img.size[0] > max_width or processed_img.size[1] > max_height:
                processed_img.thumbnail((max_width, max_height), Image.Resampling.LANCZOS)
        
        # 转换为RGB模式（如果需要）
        if processed_img.mode in ('RGBA', 'LA', 'P'):
            rgb_img = Image.new('RGB', processed_img.size, (255, 255, 255))
            if processed_img.mode == 'P':
                processed_img = processed_img.convert('RGBA')
            rgb_img.paste(processed_img, mask=processed_img.split()[-1] if processed_img.mode == 'RGBA' else None)
            processed_img = rgb_img
        
        # 保存为字节流
        img_buffer = io.BytesIO()
        quality = 95 if processing_config.get('image_quality', 'high') == 'high' else 85
        processed_img.save(img_buffer, format='JPEG', quality=quality, optimize=True)
        
        img_buffer.seek(0)
        return img_buffer.getvalue()
    
    def _generate_image_caption(self, file_path: Path) -> str:
        """生成图片标题"""
        handler_config = self.config['file_handlers']['images']
        
        if not handler_config.get('auto_caption', True):
            return ""
        
        if handler_config.get('caption_from_filename', True):
            # 从文件名生成标题
            name = file_path.stem
            
            # 移除常见的编号前缀
            name = re.sub(r'^\d+[_\-\.]*', '', name)
            
            # 替换下划线和连字符为空格
            name = re.sub(r'[_\-]', ' ', name)
            
            # 首字母大写
            name = name.title()
            
            return name
        
        return file_path.stem
    
    def process_text_file(self, file_path: Path) -> Dict[str, Any]:
        """
        处理文本文件
        
        Args:
            file_path: 文本文件路径
            
        Returns:
            处理结果字典
        """
        try:
            self.logger.info(f"处理文本文件: {file_path.name}")
            
            handler_config = self.config['file_handlers']['text']
            encoding = handler_config.get('encoding', 'utf-8')
            max_lines = handler_config.get('max_lines_preview', 50)
            
            # 读取文件内容
            with open(file_path, 'r', encoding=encoding) as f:
                lines = f.readlines()
            
            # 限制预览行数
            if len(lines) > max_lines:
                content = ''.join(lines[:max_lines])
                content += f"\n\n... (文件共{len(lines)}行，仅显示前{max_lines}行)"
            else:
                content = ''.join(lines)
            
            return {
                'file_path': file_path,
                'file_type': 'text',
                'success': True,
                'content': content,
                'line_count': len(lines),
                'error': None
            }
            
        except Exception as e:
            self.logger.error(f"处理文本文件失败 {file_path.name}: {str(e)}")
            return {
                'file_path': file_path,
                'file_type': 'text',
                'success': False,
                'content': None,
                'line_count': 0,
                'error': str(e)
            }
    
    def process_code_file(self, file_path: Path) -> Dict[str, Any]:
        """
        处理代码文件
        
        Args:
            file_path: 代码文件路径
            
        Returns:
            处理结果字典
        """
        try:
            self.logger.info(f"处理代码文件: {file_path.name}")
            
            handler_config = self.config['file_handlers']['code']
            max_lines = handler_config.get('max_lines_preview', 100)
            include_comments = handler_config.get('include_comments', True)
            
            # 读取代码文件
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 处理代码内容
            if not include_comments:
                # 移除注释（简单实现）
                processed_lines = []
                for line in lines:
                    if file_path.suffix == '.py':
                        if not line.strip().startswith('#'):
                            processed_lines.append(line)
                    elif file_path.suffix == '.sql':
                        if not line.strip().startswith('--'):
                            processed_lines.append(line)
                    else:
                        processed_lines.append(line)
                lines = processed_lines
            
            # 限制预览行数
            if len(lines) > max_lines:
                content = ''.join(lines[:max_lines])
                content += f"\n\n... (文件共{len(lines)}行，仅显示前{max_lines}行)"
            else:
                content = ''.join(lines)
            
            # 确定语言类型
            language = self._detect_code_language(file_path)
            
            return {
                'file_path': file_path,
                'file_type': 'code',
                'success': True,
                'content': content,
                'language': language,
                'line_count': len(lines),
                'error': None
            }
            
        except Exception as e:
            self.logger.error(f"处理代码文件失败 {file_path.name}: {str(e)}")
            return {
                'file_path': file_path,
                'file_type': 'code',
                'success': False,
                'content': None,
                'language': None,
                'line_count': 0,
                'error': str(e)
            }
    
    def _detect_code_language(self, file_path: Path) -> str:
        """检测代码语言"""
        suffix_map = {
            '.py': 'python',
            '.sql': 'sql',
            '.r': 'r',
            '.sas': 'sas',
            '.js': 'javascript',
            '.java': 'java',
            '.cpp': 'cpp',
            '.c': 'c',
            '.sh': 'bash'
        }
        
        return suffix_map.get(file_path.suffix.lower(), 'text')
