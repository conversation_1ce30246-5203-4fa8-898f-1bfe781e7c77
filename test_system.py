#!/usr/bin/env python3
"""
系统测试脚本
System Test Script

用于验证报告生成系统的基本功能
"""

import os
import sys
from pathlib import Path

# 添加src目录到Python路径
current_dir = Path(__file__).parent
src_dir = current_dir / "src"
sys.path.insert(0, str(src_dir))


def test_imports():
    """测试模块导入"""
    print("🔍 测试模块导入...")
    
    try:
        from enhanced_report_generator import EnhancedReportGenerator
        print("✅ 主生成器导入成功")
        
        from core.base_generator import BaseReportGenerator
        print("✅ 基础生成器导入成功")
        
        from processors.file_processor import FileProcessor
        print("✅ 文件处理器导入成功")
        
        from generators.word_generator import WordGenerator
        print("✅ Word生成器导入成功")
        
        from generators.ppt_generator import PPTGenerator
        print("✅ PPT生成器导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {str(e)}")
        return False


def test_dependencies():
    """测试依赖包"""
    print("\n🔍 测试依赖包...")
    
    required_packages = [
        ('docx', 'python-docx'),
        ('pptx', 'python-pptx'),
        ('pandas', 'pandas'),
        ('PIL', 'Pillow'),
        ('matplotlib', 'matplotlib'),
        ('yaml', 'PyYAML')
    ]
    
    missing_packages = []
    
    for package, pip_name in required_packages:
        try:
            __import__(package)
            print(f"✅ {pip_name}")
        except ImportError:
            print(f"❌ {pip_name}")
            missing_packages.append(pip_name)
    
    if missing_packages:
        print(f"\n💡 缺少包: {', '.join(missing_packages)}")
        print(f"   安装命令: pip install {' '.join(missing_packages)}")
        return False
    
    return True


def test_config():
    """测试配置文件"""
    print("\n🔍 测试配置文件...")
    
    config_path = current_dir / "config" / "enhanced_config.yaml"
    
    if not config_path.exists():
        print(f"❌ 配置文件不存在: {config_path}")
        return False
    
    try:
        import yaml
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 检查必要的配置项
        required_keys = ['document', 'paths', 'sections', 'styling']
        for key in required_keys:
            if key not in config:
                print(f"❌ 配置文件缺少必要项: {key}")
                return False
        
        print("✅ 配置文件格式正确")
        
        # 检查路径
        report_dir = Path(config['paths']['report_dir'])
        if not report_dir.is_absolute():
            report_dir = current_dir / report_dir
        
        if report_dir.exists():
            print(f"✅ 报告源目录存在: {report_dir}")
        else:
            print(f"⚠️  报告源目录不存在: {report_dir}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置文件解析失败: {str(e)}")
        return False


def test_file_scanning():
    """测试文件扫描功能"""
    print("\n🔍 测试文件扫描...")
    
    try:
        from enhanced_report_generator import EnhancedReportGenerator
        
        config_path = current_dir / "config" / "enhanced_config.yaml"
        generator = EnhancedReportGenerator(
            config_path=str(config_path),
            work_dir=str(current_dir)
        )
        
        # 扫描文件
        file_map = generator.scan_files()
        
        total_files = sum(len(files) for files in file_map.values())
        print(f"✅ 文件扫描成功，共找到 {total_files} 个文件")
        
        for section_name, files in file_map.items():
            if files:
                print(f"   📁 {section_name}: {len(files)} 个文件")
        
        return len(file_map) > 0
        
    except Exception as e:
        print(f"❌ 文件扫描失败: {str(e)}")
        return False


def test_basic_functionality():
    """测试基本功能"""
    print("\n🔍 测试基本功能...")
    
    try:
        from enhanced_report_generator import EnhancedReportGenerator
        
        config_path = current_dir / "config" / "enhanced_config.yaml"
        generator = EnhancedReportGenerator(
            config_path=str(config_path),
            work_dir=str(current_dir)
        )
        
        # 测试章节列表
        sections = generator.list_available_sections()
        print(f"✅ 可用章节: {len(sections)} 个")
        
        # 测试字体配置
        font_config = generator.get_font_config()
        print(f"✅ 字体配置: {font_config['main_font']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本功能测试失败: {str(e)}")
        return False


def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 自动化模型文档生成系统 - 系统测试")
    print("   Enhanced Report Generation System - System Test")
    print("=" * 60)
    
    tests = [
        ("模块导入", test_imports),
        ("依赖包检查", test_dependencies),
        ("配置文件", test_config),
        ("文件扫描", test_file_scanning),
        ("基本功能", test_basic_functionality)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {str(e)}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统可以正常使用。")
        print("\n💡 下一步:")
        print("   1. 准备报告文件到指定目录")
        print("   2. 运行: python run_report_generator.py")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关配置。")
        print("\n💡 建议:")
        print("   1. 安装缺少的依赖包")
        print("   2. 检查配置文件")
        print("   3. 确保报告源目录存在")
        return False


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
